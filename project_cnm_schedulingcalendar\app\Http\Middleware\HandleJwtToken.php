<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Tymon\JWTAuth\Facades\JWTAuth;
use Tymon\JWTAuth\Exceptions\TokenExpiredException;
use Ty<PERSON>\JWTAuth\Exceptions\TokenInvalidException;
use Tymon\JWTAuth\Exceptions\JWTException;

class HandleJwtToken
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Nếu người dùng đã đăng nhập, không cần xử lý JWT
        if (Auth::check()) {
            return $next($request);
        }

        // Kiểm tra xem có JWT token trong session không
        if (Session::has('jwt_token')) {
            try {
                $token = Session::get('jwt_token');
                JWTAuth::setToken($token);
                $user = JWTAuth::authenticate();
                
                if ($user) {
                    // Đăng nhập người dùng vào <PERSON> Auth
                    Auth::login($user);
                    return $next($request);
                }
            } catch (\Exception $e) {
                // Xóa token không hợp lệ khỏi session
                Session::forget('jwt_token');
            }
        }

        // Kiểm tra xem có JWT token trong cookie không
        $token = $request->cookie('jwt_token');
        if ($token) {
            try {
                JWTAuth::setToken($token);
                $user = JWTAuth::authenticate();
                
                if ($user) {
                    // Đăng nhập người dùng vào Laravel Auth
                    Auth::login($user);
                    // Lưu token vào session
                    Session::put('jwt_token', $token);
                    return $next($request);
                }
            } catch (\Exception $e) {
                // Xóa cookie không hợp lệ
                \Cookie::queue(\Cookie::forget('jwt_token'));
            }
        }

        // Tiếp tục xử lý request
        return $next($request);
    }
}
