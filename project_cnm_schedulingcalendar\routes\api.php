<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\TaskController;
use App\Http\Controllers\AuthController;
use Ty<PERSON>\JWTAuth\Facades\JWTAuth;

// routes/api.php
// Public routes
Route::post('login', [AuthController::class, 'login']);
Route::post('register', [AuthController::class, 'register']);

// Route lấy token mới
Route::post('get-token', function(Request $request) {
    try {
        $credentials = $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        \Log::info('API get-token: Đang xử lý cho email: ' . $credentials['email']);

        if (Auth::attempt($credentials)) {
            $user = Auth::user();
            \Log::info('API get-token: <PERSON><PERSON><PERSON> thực thành công cho user ID: ' . $user->id);

            try {
                $token = JWTAuth::fromUser($user);
                \Log::info('API get-token: Token đã được tạo: ' . substr($token, 0, 10) . '...');

                // Kiểm tra token đã được tạo
                try {
                    JWTAuth::setToken($token);
                    $payload = JWTAuth::getPayload()->toArray();
                    \Log::info('API get-token: Token hợp lệ, expires at: ' . date('Y-m-d H:i:s', $payload['exp']));

                    return response()->json([
                        'success' => true,
                        'token' => $token,
                        'user' => $user,
                        'expires_at' => date('Y-m-d H:i:s', $payload['exp'])
                    ], 200);
                } catch (\Exception $e) {
                    \Log::error('API get-token: Token không hợp lệ sau khi tạo: ' . $e->getMessage());
                    return response()->json([
                        'success' => false,
                        'error' => 'Lỗi xác thực token: ' . $e->getMessage()
                    ], 200);
                }
            } catch (\Exception $e) {
                \Log::error('API get-token: Lỗi tạo token: ' . $e->getMessage());
                return response()->json([
                    'success' => false,
                    'error' => 'Lỗi tạo token: ' . $e->getMessage()
                ], 200);
            }
        }

        \Log::error('API get-token: Xác thực thất bại cho email: ' . $credentials['email']);
        return response()->json([
            'success' => false,
            'error' => 'Thông tin đăng nhập không chính xác'
        ], 200);
    } catch (\Exception $e) {
        \Log::error('API get-token: Lỗi: ' . $e->getMessage());
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 200);
    }
});

// Route kiểm tra token không cần middleware
Route::get('token-info', function() {
    try {
        // Lấy token từ header Authorization
        $token = null;
        $request = request();

        if ($request->hasHeader('Authorization')) {
            $authHeader = $request->header('Authorization');
            if (strpos($authHeader, 'Bearer ') === 0) {
                $token = substr($authHeader, 7);
                \Log::info('API token-info: Token nhận được từ header: ' . substr($token, 0, 10) . '...');
            } else {
                \Log::error('API token-info: Header Authorization không đúng định dạng Bearer');
            }
        } else {
            \Log::error('API token-info: Không có header Authorization');
        }

        if (!$token) {
            \Log::error('API token-info: Token không tồn tại trong request');
            return response()->json(['valid' => false, 'error' => 'Token không tồn tại'], 200);
        }

        try {
            // Thiết lập token cho JWTAuth
            JWTAuth::setToken($token);

            // Kiểm tra token có hợp lệ không
            if (!JWTAuth::check()) {
                \Log::error('API token-info: Token không hợp lệ (JWTAuth::check() trả về false)');
                return response()->json(['valid' => false, 'error' => 'Token không hợp lệ'], 200);
            }

            // Lấy payload từ token
            $payload = JWTAuth::getPayload()->toArray();
            \Log::info('API token-info: Token hợp lệ, payload: ' . json_encode($payload));

            return response()->json([
                'valid' => true,
                'payload' => $payload,
                'expires_at' => date('Y-m-d H:i:s', $payload['exp'])
            ], 200);
        } catch (\Tymon\JWTAuth\Exceptions\TokenExpiredException $e) {
            \Log::error('API token-info: Token đã hết hạn');
            return response()->json(['valid' => false, 'error' => 'Token đã hết hạn'], 200);
        } catch (\Tymon\JWTAuth\Exceptions\TokenInvalidException $e) {
            \Log::error('API token-info: Token không hợp lệ: ' . $e->getMessage());
            return response()->json(['valid' => false, 'error' => 'Token không hợp lệ'], 200);
        } catch (\Tymon\JWTAuth\Exceptions\JWTException $e) {
            \Log::error('API token-info: Lỗi JWT: ' . $e->getMessage());
            return response()->json(['valid' => false, 'error' => 'Lỗi xử lý token: ' . $e->getMessage()], 200);
        }
    } catch (\Exception $e) {
        \Log::error('API token-info: Lỗi: ' . $e->getMessage());
        return response()->json(['valid' => false, 'error' => $e->getMessage()], 200);
    }
});

// Protected routes
Route::middleware('jwt.verify')->group(function () {
    Route::get('tasks', [TaskController::class, 'index']);
    Route::post('tasks', [TaskController::class, 'store']);
    Route::get('tasks/{task}', [TaskController::class, 'show']);
    Route::put('tasks/{task}', [TaskController::class, 'update']);
    Route::delete('tasks/{task}', [TaskController::class, 'destroy']);

    Route::post('logout', [AuthController::class, 'logout']);
    Route::post('refresh', [AuthController::class, 'refresh']);  // Thêm route để refresh token
    Route::get('user', [AuthController::class, 'me']);  // Lấy thông tin user hiện tại

    // Route kiểm tra token
    Route::get('check-token', function() {
        return response()->json(['status' => 'Token is valid', 'user' => auth()->user()]);
    });
});

?>