.toaster {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  max-width: calc(100vw - 2rem);
  position: fixed;
  left: 0;
  right: 0;
  bottom: 1rem;
  margin: auto;
  pointer-events: none;
}

.toast {
  font-weight: 500;
  color: var(--color-text-light);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-md);
  animation: toast-in var(--duration-md) ease,
    toast-out var(--duration-md) ease var(--duration-2xl);
}

.toast--success {
  background-color: var(--color-green-600);
}

.toast--error {
  background-color: var(--color-red-600);
}

@keyframes toast-in {
  from {
    opacity: 0;
    transform: translateY(1rem);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes toast-out {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}