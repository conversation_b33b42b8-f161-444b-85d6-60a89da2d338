<?php $__env->startSection('title', 'Q<PERSON>ản lý công việc - Manager'); ?>

<?php $__env->startSection('header', 'Quản lý công việc'); ?>



<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="mb-4 flex justify-between items-center">
        <h2 class="text-xl font-semibold"><PERSON>h sách công việc</h2>
    </div>

    <div class="overflow-x-auto">
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Ti<PERSON>u đề</th>
                    <th>Người dùng</th>
                    <th><PERSON><PERSON><PERSON> bắt đầu</th>
                    <th><PERSON><PERSON><PERSON> kết thúc</th>
                    <th>Trạng thái</th>
                    <th>M<PERSON><PERSON> độ ưu tiên</th>
                    <th>T<PERSON> tác</th>
                </tr>
            </thead>
            <tbody>
                <?php $__empty_1 = true; $__currentLoopData = $tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                    <td><?php echo e($task->id); ?></td>
                    <td><?php echo e($task->title); ?></td>
                    <td><?php echo e($task->creator->email); ?></td>
                    <td><?php echo e($task->start_date ? date('d/m/Y', strtotime($task->start_date)) : 'N/A'); ?></td>
                    <td><?php echo e($task->due_date ? date('d/m/Y', strtotime($task->due_date)) : 'N/A'); ?></td>
                    <td>
                        <?php if($task->status == 'pending'): ?>
                            <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">Chờ xử lý</span>
                        <?php elseif($task->status == 'in_progress'): ?>
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">Đang thực hiện</span>
                        <?php elseif($task->status == 'completed'): ?>
                            <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Hoàn thành</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <?php if($task->priority == 'low'): ?>
                            <span class="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">Thấp</span>
                        <?php elseif($task->priority == 'medium'): ?>
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">Trung bình</span>
                        <?php elseif($task->priority == 'high'): ?>
                            <span class="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">Cao</span>
                        <?php endif; ?>
                    </td>
                    <td>
                        <div class="flex items-center space-x-2">
                            <a href="<?php echo e(route('manager.edit-task', $task->id)); ?>" class="btn-sm btn-primary flex items-center justify-center" title="Sửa công việc">
                                <i class="fas fa-edit mr-1"></i> Sửa
                            </a>
                            <form action="<?php echo e(route('manager.delete-task', $task->id)); ?>" method="POST" class="inline" onsubmit="return confirm('Bạn có chắc chắn muốn xóa công việc này?')">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn-sm btn-danger flex items-center justify-center" title="Xóa công việc">
                                    <i class="fas fa-trash-alt mr-1"></i> Xóa
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="8" class="text-center py-4">Không có công việc nào.</td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('manager.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp82\htdocs\project_CMN_calendar_mem\work_management\resources\views/manager/all-tasks.blade.php ENDPATH**/ ?>