<?php $__env->startSection('styles'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/dashboard.css')); ?>">
    <style>
        /* Profile Button & Dropdown Styles */
        .profile-button {
            background: none;
            border: none;
            cursor: pointer;
            padding: 0;
            transition: transform 0.2s;
        }

        .profile-button:hover {
            transform: scale(1.05);
        }

        .profile-icon {
            width: 36px;
            height: 36px;
            background: #f0f0f0;
            color: #333;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            text-transform: uppercase;
            border: 1px solid #e0e0e0;
        }

        .profile-dropdown {
            position: absolute;
            top: 50px;
            right: 0;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
        }

        .password-change-container,
        .edit-profile-container {
            position: absolute;
            top: 0;
            right: 0;
            z-index: 1000;
            margin-top: 0;
            padding-top: 0;
        }

        .hidden {
            display: none;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php if(session('jwt_token')): ?>
        <script>
            // Lưu JWT token vào localStorage
            localStorage.setItem('jwt_token', '<?php echo e(session('jwt_token')); ?>');
            console.log('JWT token saved from session to localStorage');
        </script>
    <?php endif; ?>

    <div id="jwt-status" class="hidden fixed top-0 right-0 m-4 p-3 rounded z-50" style="display: none;"></div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const statusDiv = document.getElementById('jwt-status');

            // Đảm bảo statusDiv tồn tại
            if (!statusDiv) {
                console.error('JWT status div not found');
                return;
            }

            try {
                const token = localStorage.getItem('jwt_token');

                if (token) {
                    console.log('Found token in localStorage');

                    // Hiển thị thông báo đang kiểm tra
                    statusDiv.classList.remove('hidden');
                    statusDiv.classList.add('bg-blue-100', 'border', 'border-blue-400', 'text-blue-700');
                    statusDiv.textContent = 'Đang kiểm tra token...';

                    // Kiểm tra token bằng cách gọi API
                    fetch('/api/auth/session', {
                        method: 'GET',
                        headers: {
                            'Authorization': 'Bearer ' + token,
                            'Accept': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest',
                            'Content-Type': 'application/json'
                        },
                        credentials: 'same-origin'
                    })
                    .then(response => {
                        // Chỉ log status, không log dữ liệu nhạy cảm
                        if (response.ok) {
                            return response.json();
                        } else {
                            console.error('Token check response not OK:', response.status);
                            throw new Error('Lỗi kết nối đến API: ' + response.status);
                        }
                    })
                    .then(data => {
                        // Không log dữ liệu token để bảo mật

                        if (data && data.valid === true) {
                            // Token hợp lệ
                            if (statusDiv) {
                                statusDiv.classList.remove('bg-blue-100', 'border-blue-400', 'text-blue-700');
                                statusDiv.classList.add('bg-green-100', 'border', 'border-green-400', 'text-green-700');
                                statusDiv.textContent = 'JWT Token hợp lệ';

                                // Ẩn thông báo ngay lập tức
                                statusDiv.classList.add('hidden');
                            }
                        } else {
                            // Token không hợp lệ
                            console.error('Token không hợp lệ:', data);
                            handleInvalidToken(data && data.error ? data.error : 'Token không hợp lệ');
                        }
                    })
                    .catch(error => {
                        console.error('Error checking token:', error);

                        // Hiển thị thông báo lỗi chi tiết hơn
                        let errorMessage = 'Lỗi kiểm tra token';

                        if (error.message.includes('Failed to fetch')) {
                            errorMessage = 'Không thể kết nối đến máy chủ API. Vui lòng kiểm tra kết nối mạng.';
                        } else if (error.message.includes('404')) {
                            errorMessage = 'API không tồn tại (404). Vui lòng kiểm tra cấu hình API.';
                        } else if (error.message.includes('401')) {
                            errorMessage = 'Không có quyền truy cập API (401). Token không hợp lệ.';
                        } else {
                            errorMessage = 'Lỗi kiểm tra token: ' + error.message;
                        }

                        handleInvalidToken(errorMessage);
                    });
                } else {
                    console.log('No token found in localStorage');
                    handleInvalidToken('Bạn chưa đăng nhập');
                }
            } catch (error) {
                console.error('Error in token check script:', error);
                handleInvalidToken('Lỗi xử lý token: ' + error.message);
            }

            // Hàm xử lý token không hợp lệ
            function handleInvalidToken(message) {
                // Xóa token không hợp lệ
                try {
                    localStorage.removeItem('jwt_token');
                    console.log('Token removed from localStorage');
                } catch (e) {
                    console.error('Error removing token:', e);
                }

                // Hiển thị thông báo lỗi nhưng không chuyển hướng
                // Vì middleware sẽ xử lý việc chuyển hướng nếu cần
                console.log('Token không hợp lệ, nhưng không chuyển hướng vì middleware sẽ xử lý');
            }
        });
    </script>

    <div class="app">
        <div class="sidebar desktop-only">
            <div class="sidebar__logo">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar">
                    <path d="M8 2v4" />
                    <path d="M16 2v4" />
                    <rect width="18" height="18" x="3" y="4" rx="2" />
                    <path d="M3 10h18" />
                </svg>
                <span class="sidebar__title">Vanilla Calendar</span>
            </div>



            <button class="button button--primary button--lg" data-event-create-button>
                Create event
            </button>

            <div class="mini-calendar" data-mini-calendar>
                <div class="mini-calendar__header">
                    <time class="mini-calendar__date" data-mini-calendar-date></time>

                    <div class="mini-calendar__controls">
                        <button class="button button--icon button--secondary button--sm" data-mini-calendar-previous-button>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="button__icon">
                                <path d="m15 18-6-6 6-6" />
                            </svg>
                        </button>

                        <button class="button button--icon button--secondary button--sm" data-mini-calendar-next-button>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="button__icon">
                                <path d="m9 18 6-6-6-6" />
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="mini-calendar__content">
                    <ul class="mini-calendar__day-of-week-list">
                        <li class="mini-calendar__day-of-week">CN</li>
                        <li class="mini-calendar__day-of-week">T2</li>
                        <li class="mini-calendar__day-of-week">T3</li>
                        <li class="mini-calendar__day-of-week">T4</li>
                        <li class="mini-calendar__day-of-week">T5</li>
                        <li class="mini-calendar__day-of-week">T6</li>
                        <li class="mini-calendar__day-of-week">T7</li>
                    </ul>

                    <ul class="mini-calendar__day-list" data-mini-calendar-day-list></ul>
                </div>
            </div>
        </div>
        <main class="main">
            <div class="nav">
                <button class="button button--icon button--secondary mobile-only" data-hamburger-button>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="button__icon">
                        <line x1="4" x2="20" y1="12" y2="12" />
                        <line x1="4" x2="20" y1="6" y2="6" />
                        <line x1="4" x2="20" y1="18" y2="18" />
                    </svg>
                </button>

                <div class="nav__date-info">
                    <div class="nav__controls">
                        <button class="button button--secondary desktop-only" data-nav-today-button>
                            Today
                        </button>
                        <button class="button button--icon button--secondary mobile-only" data-nav-today-button>
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="button__icon">
                                <path d="M8 2v4" />
                                <path d="M16 2v4" />
                                <rect width="18" height="18" x="3" y="4" rx="2" />
                                <path d="M3 10h18" />
                                <path d="M8 14h.01" />
                                <path d="M12 14h.01" />
                                <path d="M16 14h.01" />
                                <path d="M8 18h.01" />
                                <path d="M12 18h.01" />
                                <path d="M16 18h.01" />
                            </svg>
                        </button>

                        <div class="nav__arrows">
                            <button class="button button--icon button--secondary" data-nav-previous-button>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="button__icon">
                                    <path d="m15 18-6-6 6-6" />
                                </svg>
                            </button>

                            <button class="button button--icon button--secondary" data-nav-next-button>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="button__icon">
                                    <path d="m9 18 6-6-6-6" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    <time class="nav__date" data-nav-date></time>
                </div>

                <div class="flex flex-col items-end ml-auto">
                    <div class="flex items-center mb-2">
                        <div class="relative">
                            <button id="profile-button" class="profile-button">
                                <div class="profile-icon">
                                    <?php echo e(substr(Auth::user()->name ?? Auth::user()->email, 0, 2)); ?>

                                </div>
                            </button>
                            <div id="profile-dropdown" class="profile-dropdown hidden">
                                <?php echo $__env->make('profile.profile-dropdown', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            </div>
                            <div id="password-change-container" class="password-change-container hidden">
                                <?php echo $__env->make('profile.password-form', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            </div>

                            <div id="edit-profile-container" class="edit-profile-container hidden">
                                <?php echo $__env->make('profile.edit-profile', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            </div>
                        </div>
                    </div>

                    <div class="select desktop-only">
                        <select class="select__select" data-view-select>
                            <option value="day">Day</option>
                            <option value="week">Week</option>
                            <option value="month" selected>Month</option>
                        </select>

                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="select__icon">
                            <path d="m6 9 6 6 6-6" />
                        </svg>
                    </div>
                </div>

                <script>
                    // Xử lý hiển thị/ẩn profile dropdown
                    document.addEventListener('DOMContentLoaded', function() {
                        const profileButton = document.getElementById('profile-button');
                        const profileDropdown = document.getElementById('profile-dropdown');

                        // Hiển thị/ẩn dropdown khi nhấn vào profile button
                        profileButton.addEventListener('click', function(e) {
                            e.stopPropagation();
                            profileDropdown.classList.toggle('hidden');
                        });

                        // Ẩn dropdown khi nhấn ra ngoài
                        document.addEventListener('click', function(e) {
                            if (!profileDropdown.contains(e.target) && e.target !== profileButton) {
                                profileDropdown.classList.add('hidden');
                            }
                        });
                    });
                </script>
            </div>
            <div class="calendar" data-calendar></div>
        </main>
    </div>

    <button class="fab mobile-only" data-event-create-button>
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="fab__icon">
            <path d="M5 12h14" />
            <path d="M12 5v14" />
        </svg>
    </button>

    <dialog class="dialog" data-dialog="event-form">
        <form class="form" data-event-form>
            <?php echo csrf_field(); ?>
            <div class="dialog__wrapper">
                <div class="dialog__header">
                    <h2 class="dialog__title" data-dialog-title></h2>
                    <button class="button button--icon button--secondary" type="button" data-dialog-close-button style="z-index: 1010;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="button__icon">
                            <path d="M18 6 6 18" />
                            <path d="m6 6 12 12" />
                        </svg>
                    </button>
                </div>

                <div class="dialog__content">
                    <div class="form__fields">
                        <input type="hidden" id="id" name="id" />

                        <div class="form__field">
                            <label class="form__label" for="title">Tiêu đề</label>
                            <input class="input input--fill" id="title" name="title" type="text" placeholder="Nhập tiêu đề công việc" required autofocus />
                        </div>

                        <div class="form__field">
                            <label class="form__label" for="description">Mô tả</label>
                            <textarea class="input input--fill" id="description" name="description" placeholder="Mô tả chi tiết công việc"></textarea>
                        </div>

                        <div class="form__field">
                            <label class="form__label" for="start_date">Ngày bắt đầu</label>
                            <input class="input input--fill" id="start_date" name="start_date" type="date" required />
                        </div>

                        <div class="form__field">
                            <label class="form__label" for="due_date">Ngày hết hạn</label>
                            <input class="input input--fill" id="due_date" name="due_date" type="date" required />
                        </div>

                        <div class="form__field">
                            <label class="form__label" for="priority">Mức độ ưu tiên</label>
                            <div class="select select--fill">
                                <select class="select__select" id="priority" name="priority">
                                    <option value="low">Thấp</option>
                                    <option value="medium" selected>Trung bình</option>
                                    <option value="high">Cao</option>
                                </select>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="select__icon">
                                    <path d="m6 9 6 6 6-6" />
                                </svg>
                            </div>
                        </div>

                        <div class="form__field">
                            <label class="form__label" for="status">Trạng thái</label>
                            <div class="select select--fill">
                                <select class="select__select" id="status" name="status">
                                    <option value="pending" selected>Chờ xử lý</option>
                                    <option value="in_progress">Đang thực hiện</option>
                                    <option value="completed">Hoàn thành</option>
                                </select>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="select__icon">
                                    <path d="m6 9 6 6 6-6" />
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="dialog__footer">
                    <div class="dialog__actions">
                        <button class="button button--secondary" type="button" data-dialog-close-button style="z-index: 1010;">
                            Hủy
                        </button>
                        <button type="submit" class="button button--primary" style="z-index: 1010;">Lưu</button>
                    </div>
                </div>
            </div>
        </form>
    </dialog>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script src="<?php echo e(asset('js/dashboard.js')); ?>"></script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp82\htdocs\project_CMN_calendar_mem\work_management\resources\views/dashboard.blade.php ENDPATH**/ ?>