.dialog {
  border: 0;
  margin: 0;
  max-height: unset;
  max-width: unset;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  background-color: transparent;
  overflow: hidden;
}

.dialog--sidebar {
  justify-content: flex-start;
}

.dialog[open] {
  display: flex;
  animation: open-dialog var(--duration-sm) forwards ease-out;
}

.dialog--sidebar[open] {
  animation-name: open-sidebar-dialog;
  animation-duration: var(--duration-md);
}

.dialog--closing[open] {
  animation: close-dialog var(--duration-sm) forwards ease-out;
}

.dialog--sidebar.dialog--closing[open] {
  animation-name: close-sidebar-dialog;
  animation-duration: var(--duration-md);
}

.dialog[open]::backdrop {
  background-color: var(--color-black);
  animation: open-backdrop var(--duration-sm) forwards ease-out;
}

.dialog--sidebar[open]::backdrop {
  animation-duration: var(--duration-md);
}

.dialog--closing[open]::backdrop {
  animation: close-backdrop var(--duration-sm) forwards ease-out;
}

.dialog--sidebar.dialog--closing[open]::backdrop {
  animation-duration: var(--duration-md);
}

.dialog__wrapper {
  margin: auto;
  border-radius: var(--border-radius-md);
  background-color: var(--color-white);
  display: flex;
  flex-direction: column;
  width: 30rem;
  max-width: calc(100vw - 2rem);
  gap: 1.5rem;
  padding: 1.5rem 0;
  max-height: calc(100vh - 2rem);
}

.dialog--sidebar .dialog__wrapper {
  margin: 0;
  max-height: unset;
  height: 100%;
  width: 18rem;
  padding: 1rem;
  border-radius: 0;
}

.dialog__header {
  flex: 0 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  padding: 0 1.5rem;
}

.dialog__header-actions {
  display: flex;
  align-items: stretch;
  gap: 0.5rem;
}

.dialog__header-actions-divider {
  flex-shrink: 0;
  width: 1px;
  background-color: var(--color-gray-100);
}

.dialog__title {
  font-size: var(--font-size-2xl);
  line-height: var(--line-height-2xl);
  font-weight: 500;
}

.dialog__content {
  flex: 1 1 auto;
  overflow-y: auto;
  padding: 0 1.5rem;
}

.dialog--sidebar .dialog__content {
  padding: 0;
}

.dialog__footer {
  flex: 0 0 auto;
  padding: 0 1.5rem;
}

.dialog__actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

@keyframes open-dialog {
  from {
    transform: scale(0.9);
    opacity: 0;
  }

  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes close-dialog {
  from {
    transform: scale(1);
    opacity: 1;
  }

  to {
    transform: scale(0.9);
    opacity: 0;
  }
}

@keyframes open-sidebar-dialog {
  from {
    transform: translateX(-100%);
  }

  to {
    transform: translateX(0);
  }
}

@keyframes close-sidebar-dialog {
  from {
    transform: translate(0);
  }

  to {
    transform: translateX(-100%);
  }
}

@keyframes open-backdrop {
  from {
    opacity: 0;
  }

  to {
    opacity: 0.85;
  }
}

@keyframes close-backdrop {
  from {
    opacity: 0.85;
  }

  to {
    opacity: 0;
  }
}