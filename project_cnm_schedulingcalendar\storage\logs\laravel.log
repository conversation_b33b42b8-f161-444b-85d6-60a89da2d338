[2025-05-20 04:21:43] local.INFO: <PERSON><PERSON> xử lý đăng nhập cho email: <EMAIL>  
[2025-05-20 04:21:43] local.INFO: Đ<PERSON><PERSON> nhập thành công cho user ID: 1  
[2025-05-20 04:21:43] local.ERROR: Carbon\Carbon::rawAddUnit(): Argument #3 ($value) must be of type int|float, string given, called in C:\xampp82\htdocs\project_CMN_calendar_mem\project_cnm_schedulingcalendar\vendor\nesbot\carbon\src\Carbon\Traits\Units.php on line 356 {"userId":1,"exception":"[object] (TypeError(code: 0): Carbon\\Carbon::rawAddUnit(): Argument #3 ($value) must be of type int|float, string given, called in C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Units.php on line 356 at C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Units.php:455)
[stacktrace]
#0 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Units.php(356): Carbon\\Carbon::rawAddUnit(Object(Carbon\\Carbon), 'minute', '60')
#1 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Date.php(2903): Carbon\\Carbon->addUnit('minute', '60', NULL)
#2 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\nesbot\\carbon\\src\\Carbon\\Traits\\Date.php(2594): Carbon\\Carbon->callModifierMethod('minute', Array)
#3 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\tymon\\jwt-auth\\src\\Claims\\Factory.php(136): Carbon\\Carbon->__call('addMinutes', Array)
#4 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\tymon\\jwt-auth\\src\\Claims\\Factory.php(106): Tymon\\JWTAuth\\Claims\\Factory->exp()
#5 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\tymon\\jwt-auth\\src\\Factory.php(143): Tymon\\JWTAuth\\Claims\\Factory->make('exp')
#6 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\tymon\\jwt-auth\\src\\Factory.php(169): Tymon\\JWTAuth\\Factory->buildClaims()
#7 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\tymon\\jwt-auth\\src\\Factory.php(85): Tymon\\JWTAuth\\Factory->buildClaimsCollection()
#8 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\tymon\\jwt-auth\\src\\JWT.php(226): Tymon\\JWTAuth\\Factory->make()
#9 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\tymon\\jwt-auth\\src\\JWT.php(74): Tymon\\JWTAuth\\JWT->makePayload(Object(App\\Models\\User))
#10 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\tymon\\jwt-auth\\src\\JWT.php(87): Tymon\\JWTAuth\\JWT->fromSubject(Object(App\\Models\\User))
#11 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Tymon\\JWTAuth\\JWT->fromUser(Object(App\\Models\\User))
#12 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\app\\Http\\Controllers\\AuthController.php(75): Illuminate\\Support\\Facades\\Facade::__callStatic('fromUser', Array)
#13 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\AuthController->login(Object(Illuminate\\Http\\Request))
#14 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AuthController), 'login')
#15 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#59 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#60 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#61 C:\\xampp82\\htdocs\\project_CMN_calendar_mem\\project_cnm_schedulingcalendar\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp82\\\\htdo...')
#62 {main}
"} 
