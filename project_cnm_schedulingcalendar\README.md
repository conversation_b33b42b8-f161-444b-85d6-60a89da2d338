# Vanilla Calendar

Hey there! In this video, we’ll create a fully responsive calendar app using just HTML, CSS, and JavaScript. Follow along to learn how to build an interactive calendar with features like event creation, real-time updates, and more. No frameworks or libraries—everything will be built from scratch!

[![Vanilla Calendar Thumbnail](https://github.com/mateuszziomekit/vanilla-calendar/blob/main/thumbnail.jpg)](https://www.youtube.com/watch?v=PXOsddcWL4g)

- [🍿 YouTube Video](https://www.youtube.com/watch?v=PXOsddcWL4g)
- [🚀 Live Website](https://vanilla-calendar.mateuszziomekit.com/)
- [💻 Source Code](https://github.com/mateuszziomekit/vanilla-calendar)

Useful links:

- [📝 Visual Studio Code](https://code.visualstudio.com/)
- [🎨 Lucide Icons](https://lucide.dev/icons)
- [🌍 Netlify Drop](https://app.netlify.com/drop)

Features:

- 📆 Month, week, and day calendar views
- 🔄 Easy date navigation
- ✏️ Create, update, and delete events
- ✅ Form validation for event details
- 💾 Persist events across page refreshes
- ⏰ Support for all-day events
- 💬 Animated dialogs and toasts for smoother interactions
- 🗂️ Mini calendar for quick navigation
- 📱 Fully responsive design
- 📑 Mobile-friendly sidebar
- 🔗 URL state persistence
- 🌐 Real-time updates across multiple tabs
- 🚀 Put the website live on the internet

Enjoy the tutorial, and don’t forget to like, share, and subscribe if you find it helpful!
