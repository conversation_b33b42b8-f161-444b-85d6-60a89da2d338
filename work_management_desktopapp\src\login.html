<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Đ<PERSON>ng nhập - Ứng dụng <PERSON> l<PERSON> việc</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f5f5f5;
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
    }

    .container {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      padding: 30px;
      width: 350px;
    }

    h1 {
      text-align: center;
      color: #333;
      margin-bottom: 20px;
    }

    .form-group {
      margin-bottom: 15px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      color: #555;
      font-weight: 500;
    }

    input {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      box-sizing: border-box;
    }

    button {
      width: 100%;
      padding: 12px;
      background-color: #4a6cf7;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
      transition: background-color 0.3s;
    }

    button:hover {
      background-color: #3a5ce5;
    }

    .error-message {
      color: #e74c3c;
      font-size: 14px;
      margin-top: 15px;
      text-align: center;
      display: none;
    }

    .loading {
      display: none;
      text-align: center;
      margin-top: 15px;
    }

    .spinner {
      border: 3px solid #f3f3f3;
      border-top: 3px solid #3498db;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Đăng nhập</h1>
    <form id="loginForm">
      <div class="form-group">
        <label for="email">Email</label>
        <input type="email" id="email" name="email" required>
      </div>
      <div class="form-group">
        <label for="password">Mật khẩu</label>
        <input type="password" id="password" name="password" required>
      </div>
      <button type="submit">Đăng nhập</button>
    </form>
    <div id="errorMessage" class="error-message"></div>
    <div id="loading" class="loading">
      <div class="spinner"></div>
      <p>Đang đăng nhập...</p>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const loginForm = document.getElementById('loginForm');
      const errorMessage = document.getElementById('errorMessage');
      const loading = document.getElementById('loading');

      // Kiểm tra kết nối API và token khi tải trang
      checkApiAndToken();

      async function checkApiAndToken() {
        try {
          // Kiểm tra kết nối API
          const isConnected = await window.api.checkApiConnection();
          if (!isConnected) {
            errorMessage.innerHTML = `<strong>Không thể kết nối đến API server</strong><br>
              <small>Hãy đảm bảo rằng server API đang chạy tại địa chỉ http://127.0.0.1:8000</small>`; 
            errorMessage.style.display = 'block';
            return;
          }

          // Kiểm tra token
          const token = await window.api.getToken();
          if (token) {
            const tokenInfo = await window.api.checkToken();
            if (tokenInfo.valid) {
              // Nếu token hợp lệ, chuyển đến trang tasks
              window.location.href = 'tasks.html';
            }
          }
        } catch (error) {
          console.error('Error checking API and token:', error);
        }
      }

      loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        // Hiển thị loading
        loading.style.display = 'block';
        errorMessage.style.display = 'none';

        try {
          const result = await window.api.login({ email, password });

          if (result.success) {
            // Đăng nhập thành công, chuyển đến trang tasks
            window.location.href = 'tasks.html';
          } else {
            // Hiển thị lỗi
            errorMessage.textContent = result.error || 'Đăng nhập thất bại';
            errorMessage.style.display = 'block';
            loading.style.display = 'none';

            // Nếu là lỗi kết nối, hiển thị thông báo đặc biệt
            if (result.connectionError) {
              errorMessage.innerHTML = `<strong>${result.error}</strong><br>
                <small>Hãy đảm bảo rằng server API đang chạy tại địa chỉ http://localhost:8000</small>`;
            }
          }
        } catch (error) {
          errorMessage.textContent = error.message || 'Đã xảy ra lỗi khi đăng nhập';
          errorMessage.style.display = 'block';
          loading.style.display = 'none';
        }


      });
    });
  </script>
</body>
</html>
