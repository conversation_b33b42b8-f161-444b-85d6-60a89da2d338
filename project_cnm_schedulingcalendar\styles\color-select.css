.color-select {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.color-select__item {
  position: relative;
  border-radius: 50%;
  cursor: pointer;
}

.color-select__input {
  position: absolute;
  opacity: 0;
  height: 0;
  width: 0;
}

.color-select__color {
  width: 2rem;
  height: 2rem;
  padding: 0.25rem;
  border-radius: 50%;
  border: 0.125rem solid var(--color-gray-300);
  transition: border-color var(--duration-sm) ease-out;
}

.color-select__input:checked+.color-select__color {
  border-color: var(--color-select-item-color);
}

.color-select__color-inner {
  background-color: var(--color-select-item-color);
  width: 100%;
  height: 100%;
  border-radius: 50%;
}