<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Tymon\JWTAuth\Facades\JWTAuth;
use Ty<PERSON>\JWTAuth\Exceptions\JWTException;

class AuthController extends Controller
{
    public function showLoginForm()
    {
        return view('login');
    }

    public function showRegisterForm()
    {
        return view('register');
    }

    public function register(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'email' => 'required|email|unique:users',
                'password' => 'required|min:6|confirmed',
            ]);

            $user = User::create([
                'email' => $validatedData['email'],
                'password' => Hash::make($validatedData['password']),
                'role' => 'user',
            ]);

            if ($request->wantsJson()) {
                return response()->json(['message' => 'Đăng ký thành công'], 201);
            }

            return redirect()->route('login')->with('success', 'Đăng ký thành công! Vui lòng đăng nhập.');
        } catch (\Exception $e) {
            Log::error('Lỗi đăng ký: ' . $e->getMessage());

            if ($request->wantsJson()) {
                return response()->json(['error' => 'Đăng ký thất bại: ' . $e->getMessage()], 500);
            }

            return redirect()->back()->with('error', 'Đăng ký thất bại: ' . $e->getMessage())->withInput();
        }
    }

    public function login(Request $request)
    {
        try {
            $credentials = $request->validate([
                'email' => 'required|email',
                'password' => 'required',
            ]);

            // Ghi log thông tin đăng nhập
            Log::info('Đang xử lý đăng nhập cho email: ' . $credentials['email']);

            // Xác thực thông qua Laravel Auth
            if (Auth::attempt($credentials, $request->filled('remember'))) {
                $request->session()->regenerate();

                // Tạo JWT token cho người dùng
                $user = Auth::user();
                Log::info('Đăng nhập thành công cho user ID: ' . $user->id);

                try {
                    $token = JWTAuth::fromUser($user);
                    Log::info('JWT token đã được tạo: ' . substr($token, 0, 10) . '...');

                    // Lưu token vào session để JavaScript có thể lấy
                    session(['jwt_token' => $token]);

                    // Kiểm tra token đã được tạo
                    try {
                        $payload = JWTAuth::setToken($token)->getPayload();
                        $expiration = $payload['exp'];
                        Log::info('JWT token hợp lệ, expires at: ' . date('Y-m-d H:i:s', $expiration));

                        // Tính thời gian hết hạn cho cookie (phút)
                        $minutes = ($expiration - time()) / 60;
                    } catch (\Exception $e) {
                        Log::error('JWT token không hợp lệ: ' . $e->getMessage());
                        $minutes = 60; // Mặc định 1 giờ nếu không lấy được từ token
                    }

                    // Nếu yêu cầu API
                    if ($request->wantsJson() || $request->ajax() || $request->expectsJson()) {
                        return response()->json(['token' => $token, 'user' => $user]);
                    }

                    // Đăng nhập người dùng vào Laravel Auth
                    Auth::login($user);

                    // Tạo cookie chứa token và chuyển hướng
                    return redirect()->intended('/dashboard')
                        ->with('jwt_token', $token)
                        ->cookie('jwt_token', $token, $minutes);
                } catch (\Exception $jwtException) {
                    Log::error('Lỗi tạo JWT token: ' . $jwtException->getMessage());
                    throw $jwtException;
                }
            }

            // Xác thực không thành công
            Log::error('Đăng nhập thất bại: Thông tin không hợp lệ cho email ' . $credentials['email']);

            if ($request->wantsJson() || $request->ajax() || $request->expectsJson()) {
                return response()->json(['error' => 'Thông tin đăng nhập không chính xác'], 401);
            }

            return back()->withErrors([
                'email' => 'Thông tin đăng nhập không chính xác',
            ])->withInput();

        } catch (\Exception $e) {
            Log::error('Lỗi đăng nhập: ' . $e->getMessage());

            if ($request->wantsJson() || $request->ajax() || $request->expectsJson()) {
                return response()->json(['error' => 'Lỗi đăng nhập: ' . $e->getMessage()], 500);
            }

            return back()->with('error', 'Lỗi đăng nhập: ' . $e->getMessage())->withInput();
        }
    }

    public function logout(Request $request)
    {
        try {
            // Xử lý đăng xuất thông qua Laravel Auth
            Auth::logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            // Nếu có token JWT, vô hiệu hóa nó
            if ($request->bearerToken()) {
                JWTAuth::invalidate(JWTAuth::getToken());
            }

            if ($request->wantsJson()) {
                return response()->json([
                    'message' => 'Đăng xuất thành công',
                    'clear_token' => true // Thêm flag để client biết cần xóa token
                ]);
            }

            // Thêm script để xóa token từ localStorage và xóa cookie
            return redirect('/login')
                ->with('clear_token', true)
                ->cookie('jwt_token', '', -1); // Xóa cookie bằng cách đặt thời gian hết hạn trong quá khứ
        } catch (JWTException $e) {
            Log::error('Lỗi đăng xuất: ' . $e->getMessage());

            if ($request->wantsJson()) {
                return response()->json(['error' => 'Không thể đăng xuất, vui lòng thử lại'], 500);
            }

            return redirect('/login');
        }
    }

    public function refresh()
    {
        try {
            // Lấy token hiện tại
            $current_token = JWTAuth::getToken();

            // Tạo token mới
            $new_token = JWTAuth::refresh($current_token);

            return response()->json(['token' => $new_token]);
        } catch (JWTException $e) {
            Log::error('Lỗi refresh token: ' . $e->getMessage());
            return response()->json(['error' => 'Không thể refresh token'], 500);
        }
    }

    public function me(Request $request)
    {
        try {
            // Nếu người dùng đã đăng nhập qua Laravel Auth
            if (Auth::check()) {
                $user = Auth::user();
                Log::info('User đã xác thực qua Laravel Auth: ' . $user->id);
                return response()->json(['valid' => true, 'user' => $user]);
            }

            // Lấy token từ request
            $token = null;

            // Kiểm tra token trong header Authorization
            $authHeader = $request->header('Authorization');
            if ($authHeader && strpos($authHeader, 'Bearer ') === 0) {
                $token = str_replace('Bearer ', '', $authHeader);
                JWTAuth::setToken($token);
            }

            // Nếu không có token trong header, kiểm tra trong session
            if (!$token && Session::has('jwt_token')) {
                $token = Session::get('jwt_token');
                JWTAuth::setToken($token);
            }

            // Nếu không có token trong header hoặc session, kiểm tra trong cookie
            if (!$token && $request->cookie('jwt_token')) {
                $token = $request->cookie('jwt_token');
                JWTAuth::setToken($token);
            }

            // Nếu không tìm thấy token ở đâu cả
            if (!$token) {
                Log::error('JWT Token không tồn tại trong request, session hoặc cookie');
                return response()->json(['valid' => false, 'error' => 'Token không tồn tại'], 401);
            }

            // Không log token để bảo mật

            // Lấy thông tin user hiện tại từ token
            $user = JWTAuth::parseToken()->authenticate();
            if (!$user) {
                Log::error('JWT User không tìm thấy');
                return response()->json(['valid' => false, 'error' => 'Không tìm thấy người dùng'], 404);
            }

            // Đăng nhập người dùng vào Laravel Auth
            Auth::login($user);

            Log::info('JWT User đã xác thực: ' . $user->id);

            // Chỉ trả về thông tin cần thiết, không trả về toàn bộ thông tin user
            return response()->json([
                'valid' => true,
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    // Không trả về các thông tin nhạy cảm khác
                ]
            ]);
        } catch (TokenExpiredException $e) {
            Log::error('JWT Token đã hết hạn: ' . $e->getMessage());
            return response()->json(['valid' => false, 'error' => 'Token đã hết hạn'], 401);
        } catch (TokenInvalidException $e) {
            Log::error('JWT Token không hợp lệ: ' . $e->getMessage());
            return response()->json(['valid' => false, 'error' => 'Token không hợp lệ'], 401);
        } catch (JWTException $e) {
            Log::error('Lỗi JWT: ' . $e->getMessage());
            return response()->json(['valid' => false, 'error' => 'Lỗi token: ' . $e->getMessage()], 500);
        }
    }
}