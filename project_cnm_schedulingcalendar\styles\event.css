.event {
  width: 100%;
  display: block;
  font-size: var(--font-size-xs);
  line-height: var(--line-height-xs);
  color: var(--color-text-dark);
  text-align: left;
  background-color: transparent;
  border-radius: var(--border-radius-md);
  border: 0;
  padding: 0.125rem 0.5rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.event--filled {
  background-color: var(--event-color);
  color: var(--color-text-light);
}

.event--dynamic {
  position: absolute;
  width: unset;
  border: 1px solid var(--color-white);
  white-space: unset;
  overflow: unset;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}

.event--dynamic .event__title {
  display: -webkit-box;
  -webkit-line-clamp: var(--event-title-max-lines);
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.event__color {
  display: inline-block;
  border-radius: 50%;
  width: 0.5rem;
  height: 0.5rem;
  background-color: var(--event-color);
  margin-right: 0.25rem;
}

.event--filled .event__color {
  display: none;
}

.event__time {
  display: none;
}

.event--dynamic .event__time {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}