import { ObservableInputTuple, OperatorFunction, Cons } from '../types';
/** @deprecated Replaced with {@link zipWith}. Will be removed in v8. */
export declare function zip<T, A extends readonly unknown[]>(otherInputs: [...ObservableInputTuple<A>]): OperatorFunction<T, Cons<T, A>>;
/** @deprecated Replaced with {@link zipWith}. Will be removed in v8. */
export declare function zip<T, A extends readonly unknown[], R>(otherInputsAndProject: [...ObservableInputTuple<A>], project: (...values: Cons<T, A>) => R): OperatorFunction<T, R>;
/** @deprecated Replaced with {@link zipWith}. Will be removed in v8. */
export declare function zip<T, A extends readonly unknown[]>(...otherInputs: [...ObservableInputTuple<A>]): OperatorFunction<T, Cons<T, A>>;
/** @deprecated Replaced with {@link zipWith}. Will be removed in v8. */
export declare function zip<T, A extends readonly unknown[], R>(...otherInputsAndProject: [...ObservableInputTuple<A>, (...values: Cons<T, A>) => R]): OperatorFunction<T, R>;
//# sourceMappingURL=zip.d.ts.map