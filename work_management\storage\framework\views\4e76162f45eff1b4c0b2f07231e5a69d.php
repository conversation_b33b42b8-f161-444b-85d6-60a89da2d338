<?php $__env->startSection('title', '<PERSON><PERSON><PERSON> c<PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON> kê - Manager'); ?>

<?php $__env->startSection('header', 'Báo cáo & Thống kê'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .stat-card {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 20px;
        text-align: center;
    }
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        margin: 10px 0;
    }
    .stat-label {
        color: #6b7280;
        font-size: 0.875rem;
    }
    .progress-bar {
        height: 8px;
        border-radius: 4px;
        background-color: #e5e7eb;
        margin-top: 10px;
        overflow: hidden;
    }
    .progress-value {
        height: 100%;
        border-radius: 4px;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="stat-card">
        <div class="stat-label">Tổng số công việc</div>
        <div class="stat-number"><?php echo e($totalTasks); ?></div>
    </div>
    
    <div class="stat-card">
        <div class="stat-label">Chờ xử lý</div>
        <div class="stat-number"><?php echo e($pendingTasks); ?></div>
        <div class="progress-bar">
            <div class="progress-value bg-yellow-500" style="width: <?php echo e($totalTasks > 0 ? ($pendingTasks / $totalTasks * 100) : 0); ?>%"></div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-label">Đang thực hiện</div>
        <div class="stat-number"><?php echo e($inProgressTasks); ?></div>
        <div class="progress-bar">
            <div class="progress-value bg-blue-500" style="width: <?php echo e($totalTasks > 0 ? ($inProgressTasks / $totalTasks * 100) : 0); ?>%"></div>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-label">Hoàn thành</div>
        <div class="stat-number"><?php echo e($completedTasks); ?></div>
        <div class="progress-bar">
            <div class="progress-value bg-green-500" style="width: <?php echo e($totalTasks > 0 ? ($completedTasks / $totalTasks * 100) : 0); ?>%"></div>
        </div>
    </div>
</div>

<div class="card">
    <h2 class="text-xl font-semibold mb-4">Thống kê theo người dùng</h2>
    
    <div class="overflow-x-auto">
        <table>
            <thead>
                <tr>
                    <th>Người dùng</th>
                    <th>Tổng số công việc</th>
                    <th>Chờ xử lý</th>
                    <th>Đang thực hiện</th>
                    <th>Hoàn thành</th>
                    <th>Tỷ lệ hoàn thành</th>
                </tr>
            </thead>
            <tbody>
                <?php $__empty_1 = true; $__currentLoopData = $userStats; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                    <td><?php echo e($user->email); ?></td>
                    <td><?php echo e($user->total_tasks); ?></td>
                    <td><?php echo e($user->pending_tasks); ?></td>
                    <td><?php echo e($user->in_progress_tasks); ?></td>
                    <td><?php echo e($user->completed_tasks); ?></td>
                    <td>
                        <?php if($user->total_tasks > 0): ?>
                            <?php echo e(round(($user->completed_tasks / $user->total_tasks) * 100, 1)); ?>%
                        <?php else: ?>
                            0%
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="6" class="text-center py-4">Không có dữ liệu.</td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('manager.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp82\htdocs\project_CMN_calendar_mem\work_management\resources\views/manager/reports.blade.php ENDPATH**/ ?>