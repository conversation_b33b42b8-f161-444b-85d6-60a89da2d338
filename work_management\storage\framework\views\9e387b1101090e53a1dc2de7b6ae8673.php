<?php $__env->startSection('title', 'Quản lý người dùng - Admin'); ?>

<?php $__env->startSection('header', 'Quản lý người dùng'); ?>

<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="mb-4 flex justify-between items-center">
        <h2 class="text-xl font-semibold">Danh sách người dùng</h2>
    </div>

    <div class="overflow-x-auto">
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Email</th>
                    <th>Vai trò</th>
                    <th>Ng<PERSON><PERSON> tạo</th>
                    <th>Số công việc</th>
                    <th>Thao tác</th>
                </tr>
            </thead>
            <tbody>
                <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr>
                    <td><?php echo e($user->id); ?></td>
                    <td><?php echo e($user->email); ?></td>
                    <td>
                        <?php if($user->role == 'admin'): ?>
                            <span class="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">Admin</span>
                        <?php elseif($user->role == 'manager'): ?>
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">Manager</span>
                        <?php else: ?>
                            <span class="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">User</span>
                        <?php endif; ?>
                    </td>
                    <td><?php echo e($user->created_at->format('d/m/Y')); ?></td>
                    <td><?php echo e($user->tasks()->count()); ?></td>
                    <td class="flex space-x-2">
                        <a href="<?php echo e(route('admin.edit-user', $user->id)); ?>" class="btn btn-sm btn-primary">
                            <i class="fas fa-edit"></i> Sửa
                        </a>
                        <?php if($user->id !== Auth::id()): ?>
                            <form action="<?php echo e(route('admin.delete-user', $user->id)); ?>" method="POST" onsubmit="return confirm('Bạn có chắc chắn muốn xóa người dùng này? Tất cả công việc của họ cũng sẽ bị xóa.')">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i> Xóa
                                </button>
                            </form>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="6" class="text-center py-4">Không có người dùng nào.</td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp82\htdocs\project_CMN_calendar_mem\work_management\resources\views/admin/users.blade.php ENDPATH**/ ?>