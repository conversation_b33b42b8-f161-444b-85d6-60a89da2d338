<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;
use <PERSON>mon\JWTAuth\Exceptions\TokenExpiredException;
use <PERSON>mon\JWTAuth\Exceptions\TokenInvalidException;
use <PERSON>mon\JWTAuth\Exceptions\JWTException;

class JwtMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            // Lấy token từ request
            $token = $request->bearerToken();
            if (!$token) {
                \Log::error('JWT Token is absent in request');
                return response()->json(['error' => 'Token is absent'], 401);
            }

            \Log::info('JWT Token received: ' . substr($token, 0, 10) . '...');

            // Xác thực token
            $user = JWTAuth::parseToken()->authenticate();
            if (!$user) {
                \Log::error('JWT User not found');
                return response()->json(['error' => 'User not found'], 401);
            }

            \Log::info('JWT User authenticated: ' . $user->id);
        } catch (TokenExpiredException $e) {
            \Log::error('JWT Token has expired: ' . $e->getMessage());
            return response()->json(['error' => 'Token has expired'], 401);
        } catch (TokenInvalidException $e) {
            \Log::error('JWT Token is invalid: ' . $e->getMessage());
            return response()->json(['error' => 'Token is invalid'], 401);
        } catch (JWTException $e) {
            \Log::error('JWT Exception: ' . $e->getMessage());
            return response()->json(['error' => 'Token error: ' . $e->getMessage()], 401);
        }

        return $next($request);
    }
}