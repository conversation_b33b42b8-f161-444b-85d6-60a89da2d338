.month-calendar {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.month-calendar__day-of-week-list {
  list-style: none;
  display: grid;
  grid-template-columns: repeat(7, minmax(0, 1fr));
  border-bottom: 1px solid var(--color-gray-300);
  padding: 0.75rem 0;
}

.month-calendar__day-of-week {
  font-size: var(--font-size-md);
  line-height: var(--line-height-md);
  text-align: center;
  font-weight: 500;
}

.month-calendar__day-list-wrapper {
  position: relative;
  flex: 1;
}

.month-calendar__day-list {
  list-style: none;
  position: absolute;
  inset: 0;
  display: grid;
  grid-template-columns: repeat(7, minmax(0, 1fr));
  overflow-y: auto;
}

.month-calendar--four-week .month-calendar__day-list {
  grid-template-rows: repeat(4, minmax(auto, 1fr));
}

.month-calendar--five-week .month-calendar__day-list {
  grid-template-rows: repeat(5, minmax(auto, 1fr));
}

.month-calendar--six-week .month-calendar__day-list {
  grid-template-rows: repeat(6, minmax(auto, 1fr));
}

.month-calendar__day {
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--color-gray-300);
  border-bottom: 1px solid var(--color-gray-300);
}

.month-calendar__day--highlight {
  background-color: var(--color-blue-50);
}

.month-calendar__day:nth-child(7n) {
  border-right: 0;
}

.month-calendar--four-week .month-calendar__day:nth-child(n + 22) {
  border-bottom: 0;
}

.month-calendar--five-week .month-calendar__day:nth-child(n + 29) {
  border-bottom: 0;
}

.month-calendar--six-week .month-calendar__day:nth-child(n + 36) {
  border-bottom: 0;
}

.month-calendar__day-label {
  color: var(--color-text-dark);
  width: 100%;
  padding: 0.5rem 0;
  background-color: transparent;
  border: 0;
  cursor: pointer;
}

.month-calendar__event-list-wrapper {
  flex-grow: 1;
  padding-bottom: 1.5rem;
}