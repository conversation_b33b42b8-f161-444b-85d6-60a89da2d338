// JWT Handler - Xử lý JWT token

// Lưu token vào localStorage
function saveToken(token) {
    if (!token) {
        console.error('Không thể lưu token vì token rỗng');
        return;
    }

    try {
        localStorage.setItem('jwt_token', token);
        // Không log token để bảo mật

        // Kiểm tra xem token đã được lưu chưa
        const savedToken = localStorage.getItem('jwt_token');
        if (savedToken) {
            console.log('Token đã được lưu thành công');
        } else {
            console.error('Token không được lưu thành công');
        }
    } catch (error) {
        console.error('Lỗi khi lưu token:', error);
    }
}

// Lấy token từ localStorage
function getToken() {
    try {
        const token = localStorage.getItem('jwt_token');
        if (token) {
            // Không log token để bảo mật
            return token;
        } else {
            console.log('Không tìm thấy token trong localStorage');
            return null;
        }
    } catch (error) {
        console.error('Lỗi khi lấy token:', error);
        return null;
    }
}

// Xóa token khỏi localStorage
function removeToken() {
    try {
        localStorage.removeItem('jwt_token');
        console.log('JWT token removed from localStorage');

        // Kiểm tra xem token đã bị xóa chưa
        const token = localStorage.getItem('jwt_token');
        if (!token) {
            console.log('Token đã được xóa thành công');
        } else {
            console.error('Token không được xóa thành công');
        }
    } catch (error) {
        console.error('Lỗi khi xóa token:', error);
    }
}

// Kiểm tra token có hợp lệ không
function checkToken(token) {
    if (!token) {
        console.error('Không thể kiểm tra token vì token rỗng');
        return Promise.reject(new Error('Token rỗng'));
    }

    // Không log token để bảo mật

    // Sử dụng API session verify để kiểm tra token
    return fetch('/api/auth/session', {
        headers: {
            'Authorization': 'Bearer ' + token,
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        // Chỉ log status khi debug, không log dữ liệu nhạy cảm
        if (response.ok) {
            return response.json();
        } else {
            console.error('Token check response not OK:', response.status);
            throw new Error('Lỗi kết nối đến API: ' + response.status);
        }
    })
    .then(data => {
        // Không log dữ liệu token để bảo mật

        if (data && data.valid === true) {
            // Kiểm tra xem token có hết hạn không
            if (data.expires_at) {
                const expiresAt = new Date(data.expires_at);
                const now = new Date();

                if (expiresAt > now) {
                    console.log('Token hợp lệ và chưa hết hạn');
                    return data;
                } else {
                    console.error('Token đã hết hạn vào:', data.expires_at);
                    throw new Error('Token đã hết hạn');
                }
            } else {
                console.log('Token hợp lệ (không có thông tin hết hạn)');
                return data;
            }
        } else {
            console.error('Token không hợp lệ:', data && data.error ? data.error : 'Không rõ lỗi');
            throw new Error(data && data.error ? data.error : 'Token không hợp lệ');
        }
    })
    .catch(error => {
        console.error('Error checking token:', error);

        // Tạo thông báo lỗi chi tiết hơn
        let errorMessage = 'Lỗi kiểm tra token';

        if (error.message.includes('Failed to fetch')) {
            errorMessage = 'Không thể kết nối đến máy chủ API. Vui lòng kiểm tra kết nối mạng.';
        } else if (error.message.includes('404')) {
            errorMessage = 'API không tồn tại (404). Vui lòng kiểm tra cấu hình API.';
        } else if (error.message.includes('401')) {
            errorMessage = 'Không có quyền truy cập API (401). Token không hợp lệ.';
        } else {
            errorMessage = 'Lỗi kiểm tra token: ' + error.message;
        }

        const customError = new Error(errorMessage);
        customError.originalError = error;
        throw customError;
    });
}

// Thiết lập Axios interceptors
function setupAxiosInterceptors() {
    if (typeof axios !== 'undefined') {
        const token = getToken();

        if (token) {
            // Thêm token vào header Authorization cho tất cả các request
            axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

            // Xử lý lỗi 401 (Unauthorized)
            axios.interceptors.response.use(
                response => response,
                error => {
                    if (error.response && error.response.status === 401) {
                        // Xóa token
                        removeToken();
                        console.log('Token removed due to 401 error');

                        // Hiển thị thông báo lỗi nếu có thể
                        try {
                            const statusDiv = document.getElementById('jwt-status');
                            if (statusDiv) {
                                statusDiv.classList.remove('hidden');
                                statusDiv.classList.add('bg-red-100', 'border', 'border-red-400', 'text-red-700');
                                statusDiv.textContent = 'Phiên đăng nhập đã hết hạn';

                                // Thêm nút để chuyển hướng đến trang đăng nhập
                                const loginBtn = document.createElement('button');
                                loginBtn.type = 'button';
                                loginBtn.className = 'mt-2 ml-2 bg-blue-500 hover:bg-blue-700 text-white p-2 rounded';
                                loginBtn.textContent = 'Đăng nhập lại';
                                loginBtn.onclick = function() {
                                    window.location.href = '/login';
                                };
                                statusDiv.appendChild(loginBtn);
                            }
                        } catch (e) {
                            console.error('Error showing status:', e);
                            // Nếu không thể hiển thị thông báo, chuyển hướng đến trang đăng nhập
                            window.location.href = '/login';
                        }
                    }
                    return Promise.reject(error);
                }
            );
        }
    }
}

// Gọi API login
function loginApi(email, password, remember = false) {
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    return fetch('/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json',
            'X-CSRF-TOKEN': csrfToken
        },
        body: JSON.stringify({
            email,
            password,
            remember
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.token) {
            saveToken(data.token);
            setupAxiosInterceptors();
            return data;
        } else {
            throw new Error(data.error || 'Đăng nhập thất bại');
        }
    });
}

// Khởi tạo khi trang được load
document.addEventListener('DOMContentLoaded', function() {
    console.log('JWT Handler initialized');

    // Kiểm tra xem localStorage có hoạt động không
    try {
        localStorage.setItem('test', 'test');
        if (localStorage.getItem('test') === 'test') {
            console.log('localStorage hoạt động bình thường');
            localStorage.removeItem('test');
        } else {
            console.error('localStorage không hoạt động đúng cách');
        }
    } catch (error) {
        console.error('localStorage không khả dụng:', error);
    }

    // Thiết lập Axios interceptors
    setupAxiosInterceptors();

    // Kiểm tra nếu đang ở trang login
    if (window.location.pathname.includes('login')) {
        const statusDiv = document.getElementById('jwt-status');
        const token = getToken();

        if (token && statusDiv) {
            // Hiển thị trạng thái token
            statusDiv.classList.remove('hidden');
            statusDiv.classList.add('bg-green-100', 'border', 'border-green-400', 'text-green-700');
            statusDiv.textContent = 'Bạn đã đăng nhập. Đang kiểm tra token...';

            // Kiểm tra token
            checkToken(token)
                .then(data => {
                    statusDiv.classList.remove('bg-green-100', 'border-green-400', 'text-green-700');
                    statusDiv.classList.add('bg-blue-100', 'border-blue-400', 'text-blue-700');
                    statusDiv.textContent = 'Bạn đã có phiên đăng nhập trước đó. Vui lòng đăng nhập lại hoặc nhấn nút bên dưới để tiếp tục.';

                    // Thêm nút để chuyển hướng đến dashboard
                    const loginForm = document.querySelector('form[action*="login"]');
                    if (loginForm) {
                        const continueBtn = document.createElement('button');
                        continueBtn.type = 'button';
                        continueBtn.className = 'mt-2 bg-blue-500 hover:bg-blue-700 text-white p-2 w-full rounded';
                        continueBtn.textContent = 'Tiếp tục phiên đăng nhập trước đó';
                        continueBtn.onclick = function() {
                            window.location.href = '/dashboard';

                            // Reload trang sau 1 giây nếu chuyển hướng không thành công
                            setTimeout(function() {
                                if (window.location.pathname !== '/dashboard') {
                                    window.location.reload();
                                }
                            }, 1000);
                        };
                        loginForm.appendChild(continueBtn);
                    }
                })
                .catch(error => {
                    console.error('Error checking token:', error);
                    statusDiv.classList.remove('bg-green-100', 'border-green-400', 'text-green-700');
                    statusDiv.classList.add('bg-red-100', 'border-red-400', 'text-red-700');
                    statusDiv.textContent = 'Token không hợp lệ hoặc đã hết hạn. Vui lòng đăng nhập lại.';
                    removeToken();
                });
        }

        // Thêm event listener cho form login
        const loginForm = document.querySelector('form[action*="login"]');
        if (loginForm) {
            loginForm.addEventListener('submit', function(e) {
                if (statusDiv) {
                    // Hiển thị thông báo đang xử lý
                    statusDiv.classList.remove('hidden');
                    statusDiv.classList.add('bg-blue-100', 'border', 'border-blue-400', 'text-blue-700');
                    statusDiv.textContent = 'Đang xử lý đăng nhập...';
                }

                // Thêm event listener để lấy token sau khi đăng nhập thành công
                window.addEventListener('load', function() {
                    const token = '{{ session("jwt_token") }}';
                    if (token && token !== '{{ session("jwt_token") }}') {
                        console.log('Token từ session:', token.substring(0, 10) + '...');
                        saveToken(token);
                    }
                });
            });
        }
    }
});
