<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\TaskController;
use App\Http\Controllers\CalendarController;

// Authentication Routes
Route::get('/', [AuthController::class, 'showLoginForm'])->name('home');
Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

Route::get('/register', [AuthController::class, 'showRegisterForm'])->name('register');
Route::post('/register', [AuthController::class, 'register']);

// Protected Routes (require authentication)
Route::middleware('auth')->group(function () {
    // Dashboard
    Route::get('/dashboard', [TaskController::class, 'dashboard'])->name('dashboard');

    // Task Routes
    Route::get('/tasks/create', [TaskController::class, 'create'])->name('tasks.create');
    Route::post('/tasks', [TaskController::class, 'store'])->name('tasks.store');
    Route::get('/tasks/{task}', [TaskController::class, 'show'])->name('tasks.show');
    Route::get('/tasks/{task}/edit', [TaskController::class, 'edit'])->name('tasks.edit');
    Route::put('/tasks/{task}', [TaskController::class, 'update'])->name('tasks.update');
    Route::delete('/tasks/{task}', [TaskController::class, 'destroy'])->name('tasks.destroy');
});

// Protected API Routes (require authentication)
Route::middleware(['auth'])->group(function () {
    // Calendar API Routes
    Route::get('/api/events', [TaskController::class, 'getEvents'])->name('events.all');
    Route::get('/api/events/day', [TaskController::class, 'getDayEvents'])->name('events.day');
    Route::get('/api/events/month', [TaskController::class, 'getMonthEvents'])->name('events.month');

    // API Routes (for compatibility with existing code)
    Route::get('/tasks', [TaskController::class, 'index']);

    // Vanilla Calendar API Routes
    Route::get('/api/calendar/events', [TaskController::class, 'getCalendarEvents'])->name('calendar.events');
    Route::post('/api/calendar/events', [TaskController::class, 'store'])->name('calendar.events.store');
    Route::get('/api/calendar/events/{id}', [TaskController::class, 'show'])->name('calendar.events.show');
    Route::put('/api/calendar/events/{id}', [TaskController::class, 'update'])->name('calendar.events.update');
    Route::delete('/api/calendar/events/{id}', [TaskController::class, 'destroy'])->name('calendar.events.destroy');
});

// Public API Routes
Route::post('/refresh', [AuthController::class, 'refresh'])->name('refresh');
Route::get('/api/auth/session', [AuthController::class, 'me'])->name('session.verify');