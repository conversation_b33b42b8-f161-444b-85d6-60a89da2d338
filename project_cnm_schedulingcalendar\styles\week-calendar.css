.week-calendar {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.week-calendar__day-of-week-list {
  list-style: none;
  display: grid;
  grid-template-columns: repeat(7, minmax(0, 1fr));
  padding: 0.5rem 0.5rem 0 0.5rem;
}

.week-calendar--day .week-calendar__day-of-week-list {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.week-calendar__day-of-week {
  display: flex;
  justify-content: center;
}

.week-calendar__day-of-week-button {
  display: flex;
  gap: 0.25rem;
  align-items: center;
  justify-content: center;
  border: 2px solid transparent;
  padding: 0;
  border-radius: var(--border-radius-md);
  background-color: transparent;
  cursor: pointer;
  transition: background-color var(--duration-sm) ease-out;
  flex-direction: column;
}

.week-calendar__day-of-week-day {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-sm);
  color: var(--color-gray-500);
}

.week-calendar__day-of-week-number {
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-md);
  line-height: var(--line-height-md);
  color: var(--color-text-dark);
  padding: 0.25rem 0.5rem;
  border: 1px solid transparent;
}

.week-calendar__day-of-week-button--highlight .week-calendar__day-of-week-number {
  border-color: var(--color-blue-600);
}

.week-calendar__day-of-week-button--selected .week-calendar__day-of-week-number {
  background-color: var(--color-blue-600);
  color: var(--color-text-light);
}

.week-calendar__all-day-list {
  position: relative;
  list-style: none;
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  padding: 0.5rem;
}

.week-calendar__all-day-list::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  box-shadow: var(--box-shadow-md);
}

.week-calendar__all-day-list-item {
  padding: 0 0.125rem;
}

.week-calendar__content {
  position: relative;
  flex: 1;
}

.week-calendar__content-inner {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  overflow-y: auto;
}

.week-calendar__time-list {
  width: 4.5rem;
}

.week-calendar__time-item {
  height: 4rem;
  text-align: center;
}

.week-calendar__time {
  font-size: var(--font-size-xs);
  line-height: var(--line-height-xs);
  color: var(--color-gray-500);
}

.week-calendar__columns {
  flex-grow: 1;
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  padding-top: 0.75rem;
}

.week-calendar__column {
  position: relative;
}

.week-calendar__cell {
  height: 4rem;
  border-top: 1px solid var(--color-gray-300);
  border-left: 1px solid var(--color-gray-300);
}

@media (min-width: 768px) {
  .week-calendar__day-of-week-button {
    flex-direction: row;
    padding: 0.125rem 0.5rem;
    gap: 0.5rem;
  }

  .week-calendar__day-of-week-button:hover {
    background-color: var(--color-gray-100);
  }

  .week-calendar__day-of-week-button--highlight {
    border-color: var(--color-blue-600);
  }

  .week-calendar__day-of-week-number {
    padding: 0;
    border: 0;
  }

  .week-calendar__day-of-week-button--selected .week-calendar__day-of-week-number {
    background-color: transparent;
    color: var(--color-text-dark);
  }

  .week-calendar__day-of-week-list {
    grid-template-columns: repeat(7, minmax(0, 1fr));
    padding-left: 4.5rem;
  }

  .week-calendar--day .week-calendar__day-of-week-list {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .week-calendar__all-day-list {
    grid-template-columns: repeat(7, minmax(0, 1fr));
    padding-left: 4.5rem;
  }

  .week-calendar--day .week-calendar__all-day-list {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .week-calendar__columns {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }

  .week-calendar--day .week-calendar__columns {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}